<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!--<com.airbnb.lottie.LottieAnimationView
        android:id="@+id/backgroundAnimationView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:lottie_rawRes="@raw/wallpaper_sample_file" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/rowAnimationView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:lottie_rawRes="@raw/row_sample_file" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/zipperAnimationView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:lottie_rawRes="@raw/zip_sample_file" />-->

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/btnTestOverlay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Test Overlay"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btnHideOverlay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Hide Overlay" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>