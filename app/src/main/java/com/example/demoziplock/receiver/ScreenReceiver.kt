package com.example.demoziplock.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.example.demoziplock.service.ZipperOverlayService

class ScreenReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "ScreenReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_SCREEN_ON -> {
                Log.d(TAG, "Screen turned on")
                showZipperOverlay(context)
            }
            
            Intent.ACTION_SCREEN_OFF -> {
                Log.d(TAG, "Screen turned off")
                hideZipperOverlay(context)
            }
            
            Intent.ACTION_USER_PRESENT -> {
                Log.d(TAG, "User present (unlocked)")
                hideZipperOverlay(context)
            }
            
            Intent.ACTION_BOOT_COMPLETED -> {
                Log.d(TAG, "Boot completed")
                // Optionally start service on boot
                // You might want to check user preferences here
            }
        }
    }

    private fun showZipperOverlay(context: Context) {
        val intent = Intent(context, ZipperOverlayService::class.java).apply {
            action = ZipperOverlayService.ACTION_SHOW_OVERLAY
        }
        context.startService(intent)
    }

    private fun hideZipperOverlay(context: Context) {
        val intent = Intent(context, ZipperOverlayService::class.java).apply {
            action = ZipperOverlayService.ACTION_HIDE_OVERLAY
        }
        context.startService(intent)
    }
}
