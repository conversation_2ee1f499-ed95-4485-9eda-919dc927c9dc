package com.example.demoziplock.service

import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import com.example.demoziplock.R
import com.example.demoziplock.view.ZipperAnimationView

class ZipperOverlayService : Service() {

    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var zipperAnimationView: ZipperAnimationView? = null
    private var isOverlayShowing = false

    companion object {
        const val ACTION_SHOW_OVERLAY = "com.example.demoziplock.SHOW_OVERLAY"
        const val ACTION_HIDE_OVERLAY = "com.example.demoziplock.HIDE_OVERLAY"
    }

    override fun onBind(intent: IBinder?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_SHOW_OVERLAY -> showOverlay()
            ACTION_HIDE_OVERLAY -> hideOverlay()
        }
        return START_STICKY
    }

    private fun showOverlay() {
        if (isOverlayShowing || !canDrawOverlays()) {
            return
        }

        try {
            // Inflate the overlay layout
            overlayView = LayoutInflater.from(this).inflate(R.layout.overlay_zipper_lock, null)
            zipperAnimationView = overlayView?.findViewById(R.id.zipperAnimationView)

            // Set up unlock listener
            zipperAnimationView?.setOnUnlockListener {
                hideOverlay()
                // Optionally, you can add additional unlock logic here
                Toast.makeText(this, "Unlocked!", Toast.LENGTH_SHORT).show()
            }

            // Set up window layout parameters
            val layoutParams = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                        WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                        WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                        WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                        WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.START
                x = 0
                y = 0
            }

            // Add the overlay view to window manager
            windowManager?.addView(overlayView, layoutParams)
            isOverlayShowing = true

        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Error showing overlay: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun hideOverlay() {
        if (!isOverlayShowing || overlayView == null) {
            return
        }

        try {
            windowManager?.removeView(overlayView)
            overlayView = null
            zipperAnimationView = null
            isOverlayShowing = false
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun canDrawOverlays(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        hideOverlay()
    }
}
