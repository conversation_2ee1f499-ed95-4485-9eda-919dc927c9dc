package com.example.demoziplock.view

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PixelFormat
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.airbnb.lottie.LottieAnimationView
import com.airbnb.lottie.LottieComposition
import com.airbnb.lottie.LottieCompositionFactory
import com.airbnb.lottie.LottieDrawable
import com.example.demoziplock.R
import kotlin.math.max
import kotlin.math.min

class ZipperAnimationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // Lottie drawables for the three animations
    private var backgroundDrawable: LottieDrawable? = null
    private var rowDrawable: LottieDrawable? = null
    private var zipperDrawable: LottieDrawable? = null

    // Touch handling variables
    private var isDragging = false
    private var startY = 0f
    private var currentProgress = 0f
    private var screenHeight = 0f

    // Animation settings
    private val maxDragPercentage = 0.8f
    private val animationDuration = 300L

    // Paint for drawing
    private val paint = Paint().apply {
        isAntiAlias = true
        isFilterBitmap = true
    }

    // Callback for unlock events
    private var onUnlockListener: (() -> Unit)? = null

    init {
        setupLottieAnimations()
    }

    private fun setupLottieAnimations() {
        // Load background animation
        LottieCompositionFactory.fromRawRes(context, R.raw.wallpaper_sample_file)
            .addListener { composition ->
                backgroundDrawable = LottieDrawable().apply {
                    this.composition = composition
                    setBounds(0, 0, width, height)
                    progress = 0f
                }
            }

        // Load row animation
        LottieCompositionFactory.fromRawRes(context, R.raw.row_sample_file)
            .addListener { composition ->
                rowDrawable = LottieDrawable().apply {
                    this.composition = composition
                    setBounds(0, 0, width, height)
                    progress = 0f
                }
            }

        // Load zipper animation
        LottieCompositionFactory.fromRawRes(context, R.raw.zip_sample_file)
            .addListener { composition ->
                zipperDrawable = LottieDrawable().apply {
                    this.composition = composition
                    setBounds(0, 0, width, height)
                    progress = 0f
                }
            }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        screenHeight = h.toFloat()
        
        // Update bounds for all drawables
        backgroundDrawable?.setBounds(0, 0, w, h)
        rowDrawable?.setBounds(0, 0, w, h)
        zipperDrawable?.setBounds(0, 0, w, h)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        // Draw animations in order: background -> row -> zipper
        backgroundDrawable?.draw(canvas)
        rowDrawable?.draw(canvas)
        zipperDrawable?.draw(canvas)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                isDragging = true
                startY = event.y
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragging) {
                    val deltaY = event.y - startY
                    val maxDragDistance = screenHeight * maxDragPercentage

                    // Calculate progress based on drag distance
                    val dragProgress = max(0f, min(1f, deltaY / maxDragDistance))
                    currentProgress = dragProgress

                    // Update all animations with the same progress
                    setAllAnimationsProgress(dragProgress)
                    invalidate()

                    // Check if fully unlocked
                    if (dragProgress >= 1f) {
                        onUnlockListener?.invoke()
                    }
                }
                return true
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isDragging = false

                // If not fully unlocked, animate back to closed state
                if (currentProgress < 1f) {
                    animateToProgress(0f)
                }
                return true
            }
        }
        return false
    }

    private fun setAllAnimationsProgress(progress: Float) {
        backgroundDrawable?.progress = progress
        rowDrawable?.progress = progress
        zipperDrawable?.progress = progress
    }

    private fun animateToProgress(targetProgress: Float) {
        val animator = ValueAnimator.ofFloat(currentProgress, targetProgress)
        animator.duration = animationDuration
        animator.addUpdateListener { animation ->
            val progress = animation.animatedValue as Float
            currentProgress = progress
            setAllAnimationsProgress(progress)
            invalidate()
        }
        animator.start()
    }

    fun setOnUnlockListener(listener: () -> Unit) {
        onUnlockListener = listener
    }

    fun resetToLocked() {
        currentProgress = 0f
        setAllAnimationsProgress(0f)
        invalidate()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // Clean up resources
        backgroundDrawable?.recycleBitmaps()
        rowDrawable?.recycleBitmaps()
        zipperDrawable?.recycleBitmaps()
    }
}
