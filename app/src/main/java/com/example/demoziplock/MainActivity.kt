package com.example.demoziplock

import android.animation.ValueAnimator
import android.os.Bundle
import android.view.MotionEvent
import androidx.appcompat.app.AppCompatActivity
import com.example.demoziplock.databinding.ActivityMainBinding
import kotlin.math.max
import kotlin.math.min

class MainActivity : AppCompatActivity() {

    private val binding by lazy { ActivityMainBinding.inflate(layoutInflater) }

    private var isDragging = false
    private var startY = 0f
    private var currentProgress = 0f
    private var screenHeight = 0f

    // Maximum drag distance as percentage of screen height
    private val maxDragPercentage = 0.8f

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        setupZipperDragEffect()
    }

    private fun setupZipperDragEffect() {
        // Get screen height
        binding.root.post {
            screenHeight = binding.root.height.toFloat()
        }

        // Set up touch listener on the zipper animation view
        binding.zipperAnimationView.setOnTouchListener { view, event ->
            handleZipperTouch(event)
        }

        // Initialize all animations to paused state
        binding.backgroundAnimationView.pauseAnimation()
        binding.rowAnimationView.pauseAnimation()
        binding.zipperAnimationView.pauseAnimation()

        // Set initial progress to 0
        setAllAnimationsProgress(0f)
    }

    private fun handleZipperTouch(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                isDragging = true
                startY = event.y
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragging) {
                    val deltaY = event.y - startY
                    val maxDragDistance = screenHeight * maxDragPercentage

                    // Calculate progress based on drag distance
                    val dragProgress = max(0f, min(1f, deltaY / maxDragDistance))
                    currentProgress = dragProgress

                    // Update all animations with the same progress
                    setAllAnimationsProgress(dragProgress)
                }
                return true
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isDragging = false

                // Animate back to closed state (progress 0) with smooth transition
                animateToProgress(0f)
                return true
            }
        }
        return false
    }

    private fun setAllAnimationsProgress(progress: Float) {
        binding.backgroundAnimationView.progress = progress
        binding.rowAnimationView.progress = progress
        binding.zipperAnimationView.progress = progress
    }

    private fun animateToProgress(targetProgress: Float) {
        val animator = ValueAnimator.ofFloat(currentProgress, targetProgress)
        animator.duration = 300 // 300ms animation
        animator.addUpdateListener { animation ->
            val progress = animation.animatedValue as Float
            currentProgress = progress
            setAllAnimationsProgress(progress)
        }
        animator.start()
    }
}